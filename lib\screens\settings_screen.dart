import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool soundEnabled = true;
  bool musicEnabled = true;
  bool vibrationsEnabled = true;
  bool notificationsEnabled = true;
  double soundVolume = 0.8;
  double musicVolume = 0.6;
  String selectedLanguage = 'English';
  String selectedTheme = 'Dark';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F3460),
            ],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 10),
                    Text(
                      'Settings',
                      style: GoogleFonts.orbitron(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Settings content
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  children: [
                    // Audio Settings
                    _buildSectionHeader('Audio Settings'),
                    _buildSettingsCard([
                      _buildSwitchTile(
                        'Sound Effects',
                        'Enable game sound effects',
                        Icons.volume_up,
                        soundEnabled,
                        (value) => setState(() => soundEnabled = value),
                      ),
                      _buildSliderTile(
                        'Sound Volume',
                        Icons.volume_up,
                        soundVolume,
                        (value) => setState(() => soundVolume = value),
                        enabled: soundEnabled,
                      ),
                      const Divider(color: Colors.white24),
                      _buildSwitchTile(
                        'Background Music',
                        'Enable background music',
                        Icons.music_note,
                        musicEnabled,
                        (value) => setState(() => musicEnabled = value),
                      ),
                      _buildSliderTile(
                        'Music Volume',
                        Icons.music_note,
                        musicVolume,
                        (value) => setState(() => musicVolume = value),
                        enabled: musicEnabled,
                      ),
                    ]),
                    
                    const SizedBox(height: 20),
                    
                    // Game Settings
                    _buildSectionHeader('Game Settings'),
                    _buildSettingsCard([
                      _buildSwitchTile(
                        'Vibrations',
                        'Enable haptic feedback',
                        Icons.vibration,
                        vibrationsEnabled,
                        (value) => setState(() => vibrationsEnabled = value),
                      ),
                      const Divider(color: Colors.white24),
                      _buildSwitchTile(
                        'Notifications',
                        'Enable push notifications',
                        Icons.notifications,
                        notificationsEnabled,
                        (value) => setState(() => notificationsEnabled = value),
                      ),
                      const Divider(color: Colors.white24),
                      _buildDropdownTile(
                        'Language',
                        'Select game language',
                        Icons.language,
                        selectedLanguage,
                        ['English', 'العربية', 'Español', 'Français'],
                        (value) => setState(() => selectedLanguage = value!),
                      ),
                    ]),
                    
                    const SizedBox(height: 20),
                    
                    // Display Settings
                    _buildSectionHeader('Display Settings'),
                    _buildSettingsCard([
                      _buildDropdownTile(
                        'Theme',
                        'Select app theme',
                        Icons.palette,
                        selectedTheme,
                        ['Dark', 'Light', 'Auto'],
                        (value) => setState(() => selectedTheme = value!),
                      ),
                    ]),
                    
                    const SizedBox(height: 20),
                    
                    // Account & Data
                    _buildSectionHeader('Account & Data'),
                    _buildSettingsCard([
                      _buildActionTile(
                        'Reset Progress',
                        'Clear all game data',
                        Icons.refresh,
                        () => _showResetDialog(),
                        isDestructive: true,
                      ),
                      const Divider(color: Colors.white24),
                      _buildActionTile(
                        'Export Data',
                        'Backup your game progress',
                        Icons.backup,
                        () => _exportData(),
                      ),
                      const Divider(color: Colors.white24),
                      _buildActionTile(
                        'Import Data',
                        'Restore from backup',
                        Icons.restore,
                        () => _importData(),
                      ),
                    ]),
                    
                    const SizedBox(height: 20),
                    
                    // About
                    _buildSectionHeader('About'),
                    _buildSettingsCard([
                      _buildInfoTile('Version', '1.0.0', Icons.info),
                      const Divider(color: Colors.white24),
                      _buildActionTile(
                        'Privacy Policy',
                        'View our privacy policy',
                        Icons.privacy_tip,
                        () => _showPrivacyPolicy(),
                      ),
                      const Divider(color: Colors.white24),
                      _buildActionTile(
                        'Terms of Service',
                        'View terms and conditions',
                        Icons.description,
                        () => _showTermsOfService(),
                      ),
                      const Divider(color: Colors.white24),
                      _buildActionTile(
                        'Rate App',
                        'Rate ArzaRush on the store',
                        Icons.star,
                        () => _rateApp(),
                      ),
                    ]),
                    
                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10, left: 5),
      child: Text(
        title,
        style: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF2196F3),
        ),
      ),
    );
  }

  Widget _buildSettingsCard(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(children: children),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.white70),
      title: Text(
        title,
        style: GoogleFonts.poppins(
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: GoogleFonts.poppins(
          color: Colors.white60,
          fontSize: 12,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: const Color(0xFF2196F3),
      ),
    );
  }

  Widget _buildSliderTile(
    String title,
    IconData icon,
    double value,
    ValueChanged<double> onChanged, {
    bool enabled = true,
  }) {
    return ListTile(
      leading: Icon(icon, color: enabled ? Colors.white70 : Colors.white38),
      title: Text(
        title,
        style: GoogleFonts.poppins(
          color: enabled ? Colors.white : Colors.white60,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Slider(
        value: value,
        onChanged: enabled ? onChanged : null,
        activeColor: const Color(0xFF2196F3),
        inactiveColor: Colors.white24,
      ),
      trailing: Text(
        '${(value * 100).round()}%',
        style: GoogleFonts.poppins(
          color: enabled ? Colors.white70 : Colors.white38,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildDropdownTile(
    String title,
    String subtitle,
    IconData icon,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.white70),
      title: Text(
        title,
        style: GoogleFonts.poppins(
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: GoogleFonts.poppins(
          color: Colors.white60,
          fontSize: 12,
        ),
      ),
      trailing: DropdownButton<String>(
        value: value,
        onChanged: onChanged,
        dropdownColor: const Color(0xFF1A1A2E),
        style: GoogleFonts.poppins(color: Colors.white),
        underline: Container(),
        items: options.map((String option) {
          return DropdownMenuItem<String>(
            value: option,
            child: Text(option),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildActionTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : Colors.white70,
      ),
      title: Text(
        title,
        style: GoogleFonts.poppins(
          color: isDestructive ? Colors.red : Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: GoogleFonts.poppins(
          color: Colors.white60,
          fontSize: 12,
        ),
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        color: Colors.white38,
        size: 16,
      ),
      onTap: onTap,
    );
  }

  Widget _buildInfoTile(String title, String value, IconData icon) {
    return ListTile(
      leading: Icon(icon, color: Colors.white70),
      title: Text(
        title,
        style: GoogleFonts.poppins(
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
      trailing: Text(
        value,
        style: GoogleFonts.poppins(
          color: Colors.white70,
          fontSize: 14,
        ),
      ),
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1A1A2E),
        title: Text(
          'Reset Progress',
          style: GoogleFonts.poppins(color: Colors.white),
        ),
        content: Text(
          'Are you sure you want to reset all your game progress? This action cannot be undone.',
          style: GoogleFonts.poppins(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement reset functionality
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(
              'Reset',
              style: GoogleFonts.poppins(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _exportData() {
    // Implement export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Data exported successfully!',
          style: GoogleFonts.poppins(),
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _importData() {
    // Implement import functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Data imported successfully!',
          style: GoogleFonts.poppins(),
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showPrivacyPolicy() {
    // Implement privacy policy display
  }

  void _showTermsOfService() {
    // Implement terms of service display
  }

  void _rateApp() {
    // Implement app rating functionality
  }
}
