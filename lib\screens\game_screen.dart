import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'game_over_screen.dart';

class GameScreen extends StatefulWidget {
  const GameScreen({super.key});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen>
    with TickerProviderStateMixin {
  late AnimationController _playerController;
  late AnimationController _backgroundController;
  late Animation<double> _playerJumpAnimation;
  
  bool isJumping = false;
  bool isSliding = false;
  bool isGameRunning = true;
  int score = 0;
  int coins = 0;
  double playerPosition = 0.0; // -1 left, 0 center, 1 right
  
  List<Obstacle> obstacles = [];
  List<Coin> gameCoins = [];
  
  @override
  void initState() {
    super.initState();
    
    _playerController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _playerJumpAnimation = Tween<double>(
      begin: 0.0,
      end: -100.0,
    ).animate(CurvedAnimation(
      parent: _playerController,
      curve: Curves.easeInOut,
    ));
    
    _startGame();
  }
  
  void _startGame() {
    // Start game loop
    _gameLoop();
  }
  
  void _gameLoop() async {
    while (isGameRunning) {
      await Future.delayed(const Duration(milliseconds: 50));
      if (mounted) {
        setState(() {
          score += 1;
          _updateObstacles();
          _updateCoins();
          _checkCollisions();
        });
      }
    }
  }
  
  void _updateObstacles() {
    // Remove obstacles that are off screen
    obstacles.removeWhere((obstacle) => obstacle.position > 1.5);
    
    // Add new obstacles randomly
    if (obstacles.isEmpty || obstacles.last.position > 0.3) {
      if (score % 100 == 0) { // Add obstacle every 100 points
        obstacles.add(Obstacle(
          position: -1.5,
          lane: (score ~/ 100) % 3 - 1, // Random lane
          type: ObstacleType.barrier,
        ));
      }
    }
    
    // Move obstacles
    for (var obstacle in obstacles) {
      obstacle.position += 0.02;
    }
  }
  
  void _updateCoins() {
    // Remove coins that are off screen
    gameCoins.removeWhere((coin) => coin.position > 1.5);
    
    // Add new coins randomly
    if (gameCoins.isEmpty || gameCoins.last.position > 0.5) {
      if (score % 50 == 0) { // Add coin every 50 points
        gameCoins.add(Coin(
          position: -1.5,
          lane: (score ~/ 50) % 3 - 1, // Random lane
        ));
      }
    }
    
    // Move coins
    for (var coin in gameCoins) {
      coin.position += 0.02;
    }
  }
  
  void _checkCollisions() {
    // Check obstacle collisions
    for (var obstacle in obstacles) {
      if (obstacle.position > -0.1 && obstacle.position < 0.1) {
        if (obstacle.lane == playerPosition.round() && !isJumping && !isSliding) {
          _gameOver();
          return;
        }
      }
    }
    
    // Check coin collection
    gameCoins.removeWhere((coin) {
      if (coin.position > -0.1 && coin.position < 0.1) {
        if (coin.lane == playerPosition.round()) {
          coins += 10;
          return true;
        }
      }
      return false;
    });
  }
  
  void _gameOver() {
    setState(() {
      isGameRunning = false;
    });
    
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => GameOverScreen(
          score: score,
          coins: coins,
        ),
      ),
    );
  }
  
  void _jump() {
    if (!isJumping && !isSliding) {
      setState(() {
        isJumping = true;
      });
      
      _playerController.forward().then((_) {
        _playerController.reverse().then((_) {
          setState(() {
            isJumping = false;
          });
        });
      });
    }
  }
  
  void _slide() {
    if (!isJumping && !isSliding) {
      setState(() {
        isSliding = true;
      });
      
      Future.delayed(const Duration(milliseconds: 500), () {
        setState(() {
          isSliding = false;
        });
      });
    }
  }
  
  void _moveLeft() {
    if (playerPosition > -1) {
      setState(() {
        playerPosition -= 1;
      });
    }
  }
  
  void _moveRight() {
    if (playerPosition < 1) {
      setState(() {
        playerPosition += 1;
      });
    }
  }
  
  @override
  void dispose() {
    _playerController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        onTap: _jump,
        onPanUpdate: (details) {
          if (details.delta.dx > 10) {
            _moveRight();
          } else if (details.delta.dx < -10) {
            _moveLeft();
          } else if (details.delta.dy > 10) {
            _slide();
          } else if (details.delta.dy < -10) {
            _jump();
          }
        },
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF87CEEB), // Sky blue
                Color(0xFF98FB98), // Light green
              ],
            ),
          ),
          child: Stack(
            children: [
              // Background elements
              _buildBackground(),
              
              // Game UI
              _buildGameUI(),
              
              // Player
              _buildPlayer(),
              
              // Obstacles
              ..._buildObstacles(),
              
              // Coins
              ..._buildCoins(),
              
              // Pause button
              _buildPauseButton(),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildBackground() {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return Positioned.fill(
          child: CustomPaint(
            painter: BackgroundPainter(_backgroundController.value),
          ),
        );
      },
    );
  }
  
  Widget _buildGameUI() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Score: $score',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      const Shadow(
                        color: Colors.black54,
                        blurRadius: 2,
                        offset: Offset(1, 1),
                      ),
                    ],
                  ),
                ),
                Text(
                  'Coins: $coins',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.amber,
                    shadows: [
                      const Shadow(
                        color: Colors.black54,
                        blurRadius: 2,
                        offset: Offset(1, 1),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildPlayer() {
    return AnimatedBuilder(
      animation: _playerJumpAnimation,
      builder: (context, child) {
        return Positioned(
          bottom: 150 + _playerJumpAnimation.value + (isSliding ? -30 : 0),
          left: MediaQuery.of(context).size.width / 2 - 25 + (playerPosition * 80),
          child: Container(
            width: 50,
            height: isSliding ? 30 : 50,
            decoration: BoxDecoration(
              color: const Color(0xFF2196F3),
              borderRadius: BorderRadius.circular(isSliding ? 15 : 25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              isSliding ? Icons.horizontal_rule : Icons.person,
              color: Colors.white,
              size: isSliding ? 20 : 30,
            ),
          ),
        );
      },
    );
  }
  
  List<Widget> _buildObstacles() {
    return obstacles.map((obstacle) {
      return Positioned(
        bottom: 150,
        left: MediaQuery.of(context).size.width / 2 - 25 + (obstacle.lane * 80),
        child: Transform.translate(
          offset: Offset(0, obstacle.position * MediaQuery.of(context).size.height),
          child: Container(
            width: 50,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(
              Icons.warning,
              color: Colors.white,
              size: 30,
            ),
          ),
        ),
      );
    }).toList();
  }
  
  List<Widget> _buildCoins() {
    return gameCoins.map((coin) {
      return Positioned(
        bottom: 170,
        left: MediaQuery.of(context).size.width / 2 - 15 + (coin.lane * 80),
        child: Transform.translate(
          offset: Offset(0, coin.position * MediaQuery.of(context).size.height),
          child: Container(
            width: 30,
            height: 30,
            decoration: const BoxDecoration(
              color: Colors.amber,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.monetization_on,
              color: Colors.white,
              size: 20,
            ),
          ),
        ),
      );
    }).toList();
  }
  
  Widget _buildPauseButton() {
    return SafeArea(
      child: Positioned(
        top: 20,
        right: 20,
        child: IconButton(
          onPressed: () {
            setState(() {
              isGameRunning = false;
            });
            Navigator.pop(context);
          },
          icon: const Icon(
            Icons.pause,
            color: Colors.white,
            size: 30,
          ),
        ),
      ),
    );
  }
}

class Obstacle {
  double position;
  int lane; // -1, 0, 1 for left, center, right
  ObstacleType type;
  
  Obstacle({
    required this.position,
    required this.lane,
    required this.type,
  });
}

class Coin {
  double position;
  int lane; // -1, 0, 1 for left, center, right
  
  Coin({
    required this.position,
    required this.lane,
  });
}

enum ObstacleType {
  barrier,
  spike,
  moving,
}

class BackgroundPainter extends CustomPainter {
  final double animationValue;
  
  BackgroundPainter(this.animationValue);
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..strokeWidth = 2;
    
    // Draw moving lines to simulate speed
    for (int i = 0; i < 10; i++) {
      final y = (i * size.height / 10 + animationValue * size.height) % size.height;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
    
    // Draw lane dividers
    final lanePaint = Paint()
      ..color = Colors.white.withOpacity(0.3)
      ..strokeWidth = 3;
    
    canvas.drawLine(
      Offset(size.width / 2 - 80, 0),
      Offset(size.width / 2 - 80, size.height),
      lanePaint,
    );
    
    canvas.drawLine(
      Offset(size.width / 2 + 80, 0),
      Offset(size.width / 2 + 80, size.height),
      lanePaint,
    );
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
