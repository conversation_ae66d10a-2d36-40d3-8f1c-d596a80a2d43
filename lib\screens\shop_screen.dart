import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ShopScreen extends StatefulWidget {
  const ShopScreen({super.key});

  @override
  State<ShopScreen> createState() => _ShopScreenState();
}

class _ShopScreenState extends State<ShopScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int playerCoins = 1250; // Example coins

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F3460),
            ],
          ),
        ),
        child: Safe<PERSON><PERSON>(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 10),
                    Text(
                      'Shop',
                      style: GoogleFonts.orbitron(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 15,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.amber.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.amber, width: 1),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.monetization_on,
                            color: Colors.amber,
                            size: 20,
                          ),
                          const SizedBox(width: 5),
                          Text(
                            playerCoins.toString(),
                            style: GoogleFonts.poppins(
                              color: Colors.amber,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              
              // Tab bar
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: TabBar(
                  controller: _tabController,
                  indicator: BoxDecoration(
                    color: const Color(0xFF2196F3),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.white60,
                  labelStyle: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  tabs: const [
                    Tab(text: 'Characters'),
                    Tab(text: 'Power-ups'),
                    Tab(text: 'Themes'),
                  ],
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Tab content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildCharactersTab(),
                    _buildPowerUpsTab(),
                    _buildThemesTab(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCharactersTab() {
    final characters = [
      ShopItem(
        id: 'char_default',
        name: 'Default Runner',
        description: 'The classic ArzaRush character',
        price: 0,
        isOwned: true,
        isEquipped: true,
        icon: Icons.person,
        color: const Color(0xFF2196F3),
      ),
      ShopItem(
        id: 'char_ninja',
        name: 'Shadow Ninja',
        description: 'Stealthy and fast runner',
        price: 500,
        isOwned: false,
        icon: Icons.person_4,
        color: Colors.purple,
      ),
      ShopItem(
        id: 'char_robot',
        name: 'Cyber Runner',
        description: 'Futuristic robotic character',
        price: 750,
        isOwned: false,
        icon: Icons.smart_toy,
        color: Colors.cyan,
      ),
      ShopItem(
        id: 'char_knight',
        name: 'Royal Knight',
        description: 'Armored medieval warrior',
        price: 1000,
        isOwned: false,
        icon: Icons.shield,
        color: Colors.amber,
      ),
    ];

    return _buildShopGrid(characters);
  }

  Widget _buildPowerUpsTab() {
    final powerUps = [
      ShopItem(
        id: 'power_magnet',
        name: 'Coin Magnet',
        description: 'Attracts coins automatically',
        price: 100,
        isOwned: true,
        icon: Icons.attractions,
        color: Colors.amber,
      ),
      ShopItem(
        id: 'power_shield',
        name: 'Shield',
        description: 'Protects from one obstacle',
        price: 150,
        isOwned: false,
        icon: Icons.security,
        color: Colors.blue,
      ),
      ShopItem(
        id: 'power_speed',
        name: 'Speed Boost',
        description: 'Increases running speed',
        price: 200,
        isOwned: false,
        icon: Icons.speed,
        color: Colors.red,
      ),
      ShopItem(
        id: 'power_jump',
        name: 'Super Jump',
        description: 'Higher and longer jumps',
        price: 250,
        isOwned: false,
        icon: Icons.keyboard_double_arrow_up,
        color: Colors.green,
      ),
    ];

    return _buildShopGrid(powerUps);
  }

  Widget _buildThemesTab() {
    final themes = [
      ShopItem(
        id: 'theme_city',
        name: 'Future City',
        description: 'Cyberpunk cityscape',
        price: 0,
        isOwned: true,
        isEquipped: true,
        icon: Icons.location_city,
        color: Colors.blue,
      ),
      ShopItem(
        id: 'theme_desert',
        name: 'Desert Oasis',
        description: 'Sandy dunes and palm trees',
        price: 300,
        isOwned: false,
        icon: Icons.landscape,
        color: Colors.orange,
      ),
      ShopItem(
        id: 'theme_forest',
        name: 'Mystic Forest',
        description: 'Enchanted woodland path',
        price: 400,
        isOwned: false,
        icon: Icons.forest,
        color: Colors.green,
      ),
      ShopItem(
        id: 'theme_space',
        name: 'Space Station',
        description: 'Futuristic space environment',
        price: 600,
        isOwned: false,
        icon: Icons.rocket_launch,
        color: Colors.purple,
      ),
    ];

    return _buildShopGrid(themes);
  }

  Widget _buildShopGrid(List<ShopItem> items) {
    return GridView.builder(
      padding: const EdgeInsets.all(20),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 15,
        mainAxisSpacing: 15,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        return _buildShopItemCard(items[index]);
      },
    );
  }

  Widget _buildShopItemCard(ShopItem item) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: item.isEquipped 
              ? const Color(0xFF2196F3)
              : Colors.white.withOpacity(0.2),
          width: item.isEquipped ? 2 : 1,
        ),
      ),
      child: Column(
        children: [
          // Item icon
          Expanded(
            flex: 3,
            child: Container(
              margin: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: item.color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: item.color, width: 2),
              ),
              child: Center(
                child: Icon(
                  item.icon,
                  size: 50,
                  color: item.color,
                ),
              ),
            ),
          ),
          
          // Item info
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Column(
                children: [
                  Text(
                    item.name,
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 5),
                  Text(
                    item.description,
                    style: GoogleFonts.poppins(
                      color: Colors.white70,
                      fontSize: 11,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
          
          // Action button
          Padding(
            padding: const EdgeInsets.all(10),
            child: SizedBox(
              width: double.infinity,
              height: 35,
              child: ElevatedButton(
                onPressed: () => _handleItemAction(item),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _getButtonColor(item),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(18),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  _getButtonText(item),
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getButtonColor(ShopItem item) {
    if (item.isEquipped) return Colors.green;
    if (item.isOwned) return const Color(0xFF2196F3);
    if (playerCoins >= item.price) return Colors.amber;
    return Colors.grey;
  }

  String _getButtonText(ShopItem item) {
    if (item.isEquipped) return 'EQUIPPED';
    if (item.isOwned) return 'EQUIP';
    if (playerCoins >= item.price) return '${item.price} COINS';
    return 'NOT ENOUGH';
  }

  void _handleItemAction(ShopItem item) {
    if (item.isEquipped) {
      // Already equipped, do nothing
      return;
    }
    
    if (item.isOwned) {
      // Equip the item
      setState(() {
        // Unequip all other items of the same type
        // Equip this item
        item.isEquipped = true;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '${item.name} equipped!',
            style: GoogleFonts.poppins(),
          ),
          backgroundColor: Colors.green,
        ),
      );
    } else if (playerCoins >= item.price) {
      // Purchase the item
      _showPurchaseDialog(item);
    } else {
      // Not enough coins
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Not enough coins! You need ${item.price - playerCoins} more coins.',
            style: GoogleFonts.poppins(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showPurchaseDialog(ShopItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1A1A2E),
        title: Text(
          'Purchase ${item.name}',
          style: GoogleFonts.poppins(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              item.icon,
              size: 60,
              color: item.color,
            ),
            const SizedBox(height: 15),
            Text(
              item.description,
              style: GoogleFonts.poppins(color: Colors.white70),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 15),
            Text(
              'Price: ${item.price} coins',
              style: GoogleFonts.poppins(
                color: Colors.amber,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                playerCoins -= item.price;
                item.isOwned = true;
                item.isEquipped = true;
              });
              Navigator.pop(context);
              
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    '${item.name} purchased and equipped!',
                    style: GoogleFonts.poppins(),
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2196F3),
            ),
            child: Text(
              'Purchase',
              style: GoogleFonts.poppins(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}

class ShopItem {
  final String id;
  final String name;
  final String description;
  final int price;
  bool isOwned;
  bool isEquipped;
  final IconData icon;
  final Color color;

  ShopItem({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.isOwned = false,
    this.isEquipped = false,
    required this.icon,
    required this.color,
  });
}
